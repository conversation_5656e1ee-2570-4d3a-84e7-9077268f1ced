import { Mail, Github, <PERSON>edin } from 'lucide-react';
import { motion } from "framer-motion";

const ContactMe = () => {
  return (
    <div className="min-h-screen bg-stone-50 dark:bg-stone-900 p-4 md:p-8">
      <div className="max-w-4xl mx-auto">
        {/* 头部标题 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-3xl font-bold text-stone-800 dark:text-stone-200 mb-2">联系刘怡然</h1>
          <p className="text-stone-600 dark:text-stone-400">随时欢迎与我取得联系</p>
        </motion.div>

        {/* 联系信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white dark:bg-stone-800 rounded-xl shadow-sm p-6"
        >
          <h2 className="text-xl font-bold text-stone-800 dark:text-stone-200 mb-6">
            直接联系方式
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 邮箱 */}
            <div className="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Mail className="text-green-600 dark:text-green-400 mr-4" />
              <div>
                <h3 className="font-medium text-stone-800 dark:text-stone-200">电子邮箱</h3>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <EMAIL>
                </a>
              </div>
            </div>

            {/* GitHub */}
            <div className="flex items-center p-4 bg-stone-50 dark:bg-stone-700/20 rounded-lg">
              <Github className="text-stone-800 dark:text-stone-200 mr-4" />
              <div>
                <h3 className="font-medium text-stone-800 dark:text-stone-200">GitHub</h3>
                <a 
                  href="https://github.com/username" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  github.com/username
                </a>
              </div>
            </div>

            {/* LinkedIn */}
            <div className="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Linkedin className="text-blue-600 dark:text-blue-400 mr-4" />
              <div>
                <h3 className="font-medium text-stone-800 dark:text-stone-200">LinkedIn</h3>
                <a 
                  href="https://linkedin.com/in/username" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  linkedin.com/in/username
                </a>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 下方留白 */}
        <div className="h-12"></div>
      </div>
    </div>
  );
};

export default ContactMe;
