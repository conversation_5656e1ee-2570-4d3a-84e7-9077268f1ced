import { HomeIcon, UserIcon, NotebookIcon, FileTextIcon, ImageIcon, MessageCircle } from "lucide-react";
import Index from "./pages/Index.jsx";
import Profile from "./pages/Profile.jsx";
import ContactMe from "./pages/ContactMe.jsx";

export const navItems = [
  {
    title: "首页",
    to: "/",
    icon: <HomeIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "个人信息",
    to: "/profile",
    icon: <UserIcon className="h-4 w-4" />,
    page: <Profile />,
  },
  {
    title: "笔记记录",
    to: "/notes",
    icon: <NotebookIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "作品集",
    to: "/portfolio",
    icon: <ImageIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "联系我",
    to: "/contact",
    icon: <MessageCircle className="h-4 w-4" />,
    page: <ContactMe />,
  },
];
