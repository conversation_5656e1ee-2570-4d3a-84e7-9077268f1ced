import { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Upload } from 'lucide-react';

const AvatarUploadModal = ({ isOpen, onClose, onSave }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    if (previewUrl) {
      onSave(previewUrl);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="relative bg-white dark:bg-stone-800 rounded-lg p-6 w-[90%] max-w-md z-50"
      >
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-stone-500 hover:text-stone-700 dark:text-stone-400 dark:hover:text-stone-200"
        >
          <X className="h-5 w-5" />
        </button>

        <h3 className="text-lg font-semibold mb-4 text-stone-800 dark:text-stone-200">
          更新头像
        </h3>

        <div className="space-y-4">
          <div className="flex flex-col items-center justify-center border-2 border-dashed border-stone-300 dark:border-stone-600 rounded-lg p-6">
            {previewUrl ? (
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="预览"
                  className="w-32 h-32 rounded-full object-cover"
                />
                <button
                  onClick={() => {
                    setSelectedFile(null);
                    setPreviewUrl(null);
                  }}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <label className="flex flex-col items-center cursor-pointer">
                <Upload className="h-8 w-8 text-stone-400" />
                <span className="mt-2 text-sm text-stone-600 dark:text-stone-400">
                  点击或拖拽上传图片
                </span>
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleFileSelect}
                />
              </label>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-stone-600 dark:text-stone-400 hover:bg-stone-100 dark:hover:bg-stone-700 rounded-lg"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={!previewUrl}
              className={`px-4 py-2 text-sm text-white rounded-lg ${
                previewUrl
                  ? 'bg-blue-500 hover:bg-blue-600'
                  : 'bg-blue-300 cursor-not-allowed'
              }`}
            >
              保存
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default AvatarUploadModal;
