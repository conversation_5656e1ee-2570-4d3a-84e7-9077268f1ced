import { Link, useLocation } from "react-router-dom";
import { navItems } from "../nav-items";

const Sidebar = () => {
  const { pathname } = useLocation();

  return (
    <div className="w-64 h-full border-r border-stone-200 dark:border-stone-800 bg-white dark:bg-stone-950 p-4 flex flex-col">
      <div className="mb-8">
        <h1 className="text-xl font-bold text-stone-800 dark:text-stone-200">刘怡然</h1>
        <p className="text-sm text-stone-500 dark:text-stone-400">AI产品经理实习生</p>
      </div>
      
      <nav className="flex-1 space-y-1">
        {navItems.map((item) => (
          <Link
            key={item.to}
            to={item.to}
            className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
              pathname === item.to
                ? "bg-stone-100 dark:bg-stone-800 text-stone-900 dark:text-stone-50"
                : "text-stone-600 hover:bg-stone-100 dark:text-stone-400 dark:hover:bg-stone-800"
            }`}
          >
            <span className="mr-3">{item.icon}</span>
            {item.title}
          </Link>
        ))}
      </nav>

      <div className="mt-auto pt-4 border-t border-stone-200 dark:border-stone-800">
        <div className="flex items-center px-4 py-3 rounded-lg text-sm font-medium text-stone-600 dark:text-stone-400">
          <span className="w-8 h-8 rounded-full bg-stone-200 dark:bg-stone-700 flex items-center justify-center mr-3">
            <span className="text-xs">LY</span>
          </span>
          <div>
            <p className="font-medium text-stone-800 dark:text-stone-200">刘怡然</p>
            <p className="text-xs text-stone-500 dark:text-stone-500"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
