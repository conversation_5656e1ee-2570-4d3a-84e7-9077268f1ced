import { Mail, Gith<PERSON>, <PERSON>edin, Phone, BookOpen, Smile, Camera, Edit } from 'lucide-react';
import { motion } from "framer-motion";
import { useState } from 'react';
import AvatarUploadModal from '../components/AvatarUploadModal';

const Profile = () => {
  const [avatar, setAvatar] = useState(
    "https://nocode.meituan.com/photo/search?keyword=professional,woman,portrait&width=200&height=200"
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSaveAvatar = (newAvatar) => {
    setAvatar(newAvatar);
    // 这里应该调用API保存头像到服务器
    // 实际应用中，应该上传到服务器并获取不同尺寸的缩略图URL
  };

  return (
    <div className="min-h-screen bg-stone-50 dark:bg-stone-900 p-4 md:p-8">
      <AvatarUploadModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveAvatar}
      />
      
      <div className="max-w-4xl mx-auto">
        {/* 个人信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white dark:bg-stone-800 rounded-xl shadow-sm p-6 md:p-8"
        >
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
            {/* 头像区域 */}
            <div className="flex-shrink-0 relative group">
              <img 
                src={avatar} 
                className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-4 border-blue-100 dark:border-blue-900"
                alt="个人头像"
              />
              <button
                onClick={() => setIsModalOpen(true)}
                className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <div className="bg-white p-2 rounded-full">
                  <Camera className="h-5 w-5 text-stone-800" />
                </div>
              </button>
              <div className="absolute -bottom-2 -right-2 bg-blue-500 rounded-full p-1">
                <Edit className="h-4 w-4 text-white" />
              </div>
            </div>
            
            {/* 基本信息 */}
            <div className="flex-1 text-center md:text-left">
              <h1 className="text-2xl md:text-3xl font-bold text-stone-800 dark:text-stone-200 mb-2">刘怡然</h1>
              <p className="text-lg text-stone-600 dark:text-stone-400 mb-4">AI产品经理实习生</p>
              
              <div className="flex flex-wrap justify-center md:justify-start gap-2 mb-6">
                <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-md animate-pulse">
                  ENTP
                </span>
                <span className="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-sm rounded-md font-bold">
                  执行力MAX
                </span>
                <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-md animate-bounce">
                  灵魂自带永动装置
                </span>
              </div>
              
              <p className="text-stone-600 dark:text-stone-400 text-base leading-relaxed mb-6">
                成长的每一步都算数
                <img 
                  src="https://nocode.meituan.com/photo/search?keyword=thumbs+up,cartoon&width=100&height=100" 
                  className="w-12 h-12 inline-block ml-2"
                  alt="点赞表情"
                />
              </p>
            </div>
          </div>

          {/* 联系信息 */}
          <div className="mt-8 pt-6 border-t border-stone-200 dark:border-stone-700">
            <h2 className="text-xl font-bold text-stone-800 dark:text-stone-200 mb-4">联系方式</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 邮箱 */}
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center p-3 bg-stone-50 dark:bg-stone-700/20 rounded-lg hover:bg-stone-100 dark:hover:bg-stone-700/40 transition-colors"
              >
                <Mail className="text-stone-600 dark:text-stone-300 mr-3" />
                <div>
                  <p className="text-sm text-stone-500 dark:text-stone-400">电子邮箱</p>
                  <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
                </div>
              </a>

              {/* 电话 */}
              <a 
                href="tel:+8613812345678" 
                className="flex items-center p-3 bg-stone-50 dark:bg-stone-700/20 rounded-lg hover:bg-stone-100 dark:hover:bg-stone-700/40 transition-colors"
              >
                <Phone className="text-stone-600 dark:text-stone-300 mr-3" />
                <div>
                  <p className="text-sm text-stone-500 dark:text-stone-400">联系电话</p>
                  <p className="text-blue-600 dark:text-blue-400">+86 138 1234 5678</p>
                </div>
              </a>

              {/* GitHub */}
              <a 
                href="https://github.com/username" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-stone-50 dark:bg-stone-700/20 rounded-lg hover:bg-stone-100 dark:hover:bg-stone-700/40 transition-colors"
              >
                <Github className="text-stone-600 dark:text-stone-300 mr-3" />
                <div>
                  <p className="text-sm text-stone-500 dark:text-stone-400">GitHub</p>
                  <p className="text-blue-600 dark:text-blue-400">github.com/username</p>
                </div>
              </a>

              {/* LinkedIn */}
              <a 
                href="https://linkedin.com/in/username" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-stone-50 dark:bg-stone-700/20 rounded-lg hover:bg-stone-100 dark:hover:bg-stone-700/40 transition-colors"
              >
                <Linkedin className="text-stone-600 dark:text-stone-300 mr-3" />
                <div>
                  <p className="text-sm text-stone-500 dark:text-stone-400">LinkedIn</p>
                  <p className="text-blue-600 dark:text-blue-400">linkedin.com/in/username</p>
                </div>
              </a>
            </div>
          </div>
        </motion.div>

        {/* 教育背景卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="bg-white dark:bg-stone-800 rounded-xl shadow-sm p-6 mt-6"
        >
          <div className="flex items-center mb-6">
            <BookOpen className="text-blue-600 dark:text-blue-400 mr-3" />
            <h2 className="text-xl font-bold text-stone-800 dark:text-stone-200">教育背景</h2>
          </div>

          <div className="space-y-6">
            {/* 西安交通大学 */}
            <div className="space-y-2">
              <h3 className="text-lg md:text-xl font-bold text-blue-600 dark:text-blue-400">西安交通大学</h3>
              <p className="text-base md:text-lg text-stone-700 dark:text-stone-300">力学硕士</p>
              <p className="text-sm md:text-base text-stone-500 dark:text-stone-500">2024-2027</p>
            </div>
          </div>
        </motion.div>

        {/* 个人趣事模块 */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="bg-white dark:bg-stone-800 rounded-xl shadow-sm p-6 mt-6"
        >
          <div className="flex items-center mb-6">
            <Smile className="text-blue-600 dark:text-blue-400 mr-3" />
            <h2 className="text-xl font-bold text-stone-800 dark:text-stone-200">个人趣事</h2>
          </div>
          
          <div className="space-y-6">
            {/* 小糗事 */}
            <div className="border-b border-stone-200 dark:border-stone-700 pb-6">
              <h3 className="font-medium text-lg text-stone-800 dark:text-stone-200 mb-3">一件小糗事</h3>
              <div className="flex flex-col md:flex-row items-start gap-4">
                <p className="text-stone-600 dark:text-stone-400 flex-1">
                  众目睽睽之下平地摔，当时恨不得找个地缝钻进去...
                </p>
                <img 
                  src="https://nocode.meituan.com/photo/search?keyword=falling,cartoon&width=200&height=200" 
                  className="w-24 h-24 rounded-lg mx-auto object-cover"
                  alt="摔倒糗事"
                />
              </div>
            </div>

            {/* 个人缺点 */}
            <div>
              <h3 className="font-medium text-lg text-stone-800 dark:text-stone-200 mb-3">个人缺点</h3>
              <div className="flex flex-col md:flex-row items-start gap-4">
                <p className="text-stone-600 dark:text-stone-400 flex-1">
                  容易提前焦虑
                </p>
                <img 
                  src="https://nocode.meituan.com/photo/search?keyword=anxious,cartoon&width=200&height=200" 
                  className="w-24 h-24 rounded-lg mx-auto object-cover"
                  alt="焦虑表情"
                />
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Profile;
