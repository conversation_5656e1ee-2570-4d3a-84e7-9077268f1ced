import { Link, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { Home, User, NotebookText, Image, MessageCircle } from "lucide-react";

const MobileNav = () => {
  const { pathname } = useLocation();
  const [visible, setVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setVisible(false);
      } else if (currentScrollY < lastScrollY || currentScrollY < 100) {
        setVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  const navItems = [
    {
      title: "首页",
      to: "/",
      icon: <Home className="h-5 w-5" />,
    },
    {
      title: "个人",
      to: "/profile",
      icon: <User className="h-5 w-5" />,
    },
    {
      title: "笔记",
      to: "/notes",
      icon: <NotebookText className="h-5 w-5" />,
    },
    {
      title: "作品",
      to: "/portfolio",
      icon: <Image className="h-5 w-5" />,
    },
    {
      title: "联系我",
      to: "/contact",
      icon: <MessageCircle className="h-5 w-5" />,
    },
  ];

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 h-16 bg-white/95 dark:bg-stone-950/95 backdrop-blur-md border-t border-stone-200 dark:border-stone-800 z-50 transition-transform duration-300 md:hidden ${
        visible ? "translate-y-0" : "translate-y-full"
      }`}
    >
      <div className="flex justify-around h-full">
        {navItems.map((item) => (
          <Link
            key={item.to}
            to={item.to}
            className={`flex-1 flex flex-col items-center justify-center min-w-[60px] transition-colors ${
              pathname === item.to
                ? "text-blue-600 dark:text-blue-400"
                : "text-stone-600 dark:text-stone-400 hover:text-blue-500 dark:hover:text-blue-400"
            }`}
          >
            <div className="flex flex-col items-center justify-center h-full w-full">
              <div className="flex items-center justify-center h-8 w-8">
                {item.icon}
              </div>
              <span className="text-xs mt-1">{item.title}</span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default MobileNav;
