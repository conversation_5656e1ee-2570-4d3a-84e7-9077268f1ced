import { motion } from "framer-motion";
import { useState } from "react";

const Index = () => {
  const [darkMode, setDarkMode] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`min-h-screen ${darkMode ? 'bg-stone-900' : 'bg-stone-50'} p-4 md:p-8`}
    >
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8 md:mb-12">
          <motion.h1 
            className="text-2xl md:text-4xl font-bold mb-3 md:mb-4 text-stone-800 dark:text-stone-200"
            initial={{ y: -20 }}
            animate={{ y: 0 }}
          >
            欢迎来到我的空间
          </motion.h1>
          <p className={`text-base md:text-lg ${darkMode ? 'text-stone-400' : 'text-stone-600'}`}>
            {window.innerWidth > 768 ? "使用左侧菜单浏览内容" : "使用底部导航浏览内容"}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="bg-white dark:bg-stone-800 p-4 md:p-6 rounded-xl shadow-sm border border-stone-200 dark:border-stone-700">
            <h2 className="text-lg md:text-xl font-bold mb-3 md:mb-4 text-stone-800 dark:text-stone-200">最新动态</h2>
            <p className="text-stone-600 dark:text-stone-400 text-sm md:text-base">
              最近更新了个人作品集，添加了三个新项目。
            </p>
          </div>

          <div className="bg-white dark:bg-stone-800 p-4 md:p-6 rounded-xl shadow-sm border border-stone-200 dark:border-stone-700">
            <h2 className="text-lg md:text-xl font-bold mb-3 md:mb-4 text-stone-800 dark:text-stone-200">近期笔记</h2>
            <p className="text-stone-600 dark:text-stone-400 text-sm md:text-base">
              整理了React性能优化的10个技巧。
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Index;
