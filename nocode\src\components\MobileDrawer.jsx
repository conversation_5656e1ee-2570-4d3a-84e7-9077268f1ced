import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Menu } from "lucide-react";
import { navItems } from "../nav-items";
import { Link, useLocation } from "react-router-dom";

const MobileDrawer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { pathname } = useLocation();

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    let touchStartX = 0;
    let touchEndX = 0;

    const handleTouchStart = (e) => {
      touchStartX = e.changedTouches[0].screenX;
    };

    const handleTouchEnd = (e) => {
      touchEndX = e.changedTouches[0].screenX;
      if (touchStartX - touchEndX > 50) {
        setIsOpen(false);
      }
    };

    document.addEventListener("touchstart", handleTouchStart);
    document.addEventListener("touchend", handleTouchEnd);

    return () => {
      document.removeEventListener("touchstart", handleTouchStart);
      document.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isOpen]);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="fixed top-4 left-4 z-40 p-2 rounded-full bg-white/80 dark:bg-stone-800/80 backdrop-blur-md shadow-sm border border-stone-200 dark:border-stone-700 md:hidden"
      >
        <Menu className="h-6 w-6 text-stone-800 dark:text-stone-200" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              onClick={() => setIsOpen(false)}
              className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            />

            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ type: "tween", duration: 0.35, ease: "easeInOut" }}
              className="fixed top-0 left-0 z-50 w-[75%] max-w-xs h-full bg-white dark:bg-stone-900 shadow-xl"
            >
              <div className="h-full flex flex-col p-4">
                <div className="flex justify-end">
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 rounded-full hover:bg-stone-100 dark:hover:bg-stone-800"
                  >
                    <X className="h-6 w-6 text-stone-800 dark:text-stone-200" />
                  </button>
                </div>

                <nav className="flex-1 mt-6 space-y-2">
                  {navItems.map((item) => (
                    <Link
                      key={item.to}
                      to={item.to}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center px-4 py-3 rounded-lg text-base font-medium transition-colors ${
                        pathname === item.to
                          ? "bg-stone-100 dark:bg-stone-800 text-stone-900 dark:text-stone-50"
                          : "text-stone-600 hover:bg-stone-100 dark:text-stone-400 dark:hover:bg-stone-800"
                      }`}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.title}
                    </Link>
                  ))}
                </nav>

                <div className="mt-auto pt-4 border-t border-stone-200 dark:border-stone-800">
                  <div className="flex items-center px-4 py-3 rounded-lg text-sm font-medium text-stone-600 dark:text-stone-400">
                    <span className="w-8 h-8 rounded-full bg-stone-200 dark:bg-stone-700 flex items-center justify-center mr-3">
                      <span className="text-xs">LY</span>
                    </span>
                    <div>
                      <p className="font-medium text-stone-800 dark:text-stone-200">刘怡然</p>
                      <p className="text-xs text-stone-500 dark:text-stone-500"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileDrawer;
