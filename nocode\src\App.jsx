import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route } from "react-router-dom";
import { navItems } from "./nav-items";
import MobileDrawer from "./components/MobileDrawer";
import Sidebar from "./components/Sidebar";
import MobileNav from "./components/MobileNav";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <HashRouter>
        <div className="flex h-screen bg-stone-50 dark:bg-stone-900">
          {/* 桌面端侧边栏 - 仅在大于md屏幕显示 */}
          <div className="hidden md:block">
            <Sidebar />
          </div>
          
          {/* 主内容区域 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 移动端抽屉菜单 */}
            <MobileDrawer />
            
            {/* 主内容 */}
            <main className="flex-1 overflow-auto relative">
              <Routes>
                {navItems.map(({ to, page }) => (
                  <Route key={to} path={to} element={page} />
                ))}
              </Routes>
            </main>
            
            {/* 移动端底部导航 - 仅在小于md屏幕显示 */}
            <div className="md:hidden">
              <MobileNav />
            </div>
          </div>
        </div>
      </HashRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
